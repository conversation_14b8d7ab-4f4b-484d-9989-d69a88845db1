# Coolify Docker Compose Configuration
#
# For CrowdSec integration (coolify+), you must enable proxy logging:
# 1. Copy config/coolify/proxy/docker-compose.override.yml to /data/coolify/proxy/
# 2. Restart coolify-proxy: docker restart coolify-proxy
# This enables access.log generation required for CrowdSec monitoring.

  coolify:
    image: "${REGISTRY_URL:-ghcr.io}/coollabsio/coolify:${LATEST_IMAGE:-latest}"
    container_name: coolify
    restart: always
    working_dir: /var/www/html
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - coolify
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      soketi:
        condition: service_healthy
    volumes:
      - type: bind
        source: .env
        target: /var/www/html/.env
        read_only: true
      - /data/coolify/ssh:/var/www/html/storage/app/ssh
      - /data/coolify/applications:/var/www/html/storage/app/applications
      - /data/coolify/databases:/var/www/html/storage/app/databases
      - /data/coolify/services:/var/www/html/storage/app/services
      - /data/coolify/backups:/var/www/html/storage/app/backups
      - /data/coolify/webhooks-during-maintenance:/var/www/html/storage/app/webhooks-during-maintenance
    environment:
      - APP_ENV=${APP_ENV:-production}
      - PHP_MEMORY_LIMIT=${PHP_MEMORY_LIMIT:-256M}
      - PHP_FPM_PM_CONTROL=${PHP_FPM_PM_CONTROL:-dynamic}
      - PHP_FPM_PM_START_SERVERS=${PHP_FPM_PM_START_SERVERS:-1}
      - PHP_FPM_PM_MIN_SPARE_SERVERS=${PHP_FPM_PM_MIN_SPARE_SERVERS:-1}
      - PHP_FPM_PM_MAX_SPARE_SERVERS=${PHP_FPM_PM_MAX_SPARE_SERVERS:-10}
    env_file:
      - .env
    ports:
      - "${APP_PORT:-8000}:8080"
    expose:
      - "${APP_PORT:-8000}"
    healthcheck:
      test: curl --fail http://127.0.0.1:8080/api/health || exit 1
      interval: 5s
      retries: 10
      timeout: 2s
  postgres:
    image: postgres:15-alpine
    container_name: coolify-db
    restart: always
    volumes:
      - coolify-db:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: "${DB_USERNAME:-coolify}"
      POSTGRES_PASSWORD: "${DB_PASSWORD:-coolify}"
      POSTGRES_DB: "${DB_DATABASE:-coolify}"
    networks:
      - coolify
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-coolify}", "-d", "${DB_DATABASE:-coolify}"]
      interval: 5s
      retries: 10
      timeout: 2s
  redis:
    image: redis:7-alpine
    container_name: coolify-redis
    restart: always
    command: redis-server --save 20 1 --loglevel warning --requirepass ${REDIS_PASSWORD:-coolify}
    environment:
      REDIS_PASSWORD: "${REDIS_PASSWORD:-coolify}"
    volumes:
      - coolify-redis:/data
    networks:
      - coolify
    healthcheck:
      test: redis-cli ping
      interval: 5s
      retries: 10
      timeout: 2s
  soketi:
    image: '${REGISTRY_URL:-ghcr.io}/coollabsio/coolify-realtime:1.0.10'
    container_name: coolify-realtime
    extra_hosts:
      - host.docker.internal:host-gateway
    restart: always
    ports:
      - "${SOKETI_PORT:-6001}:6001"
      - "6002:6002"
    volumes:
      - /data/coolify/ssh:/var/www/html/storage/app/ssh
    environment:
      APP_NAME: "${APP_NAME:-Coolify}"
      SOKETI_DEBUG: "${SOKETI_DEBUG:-false}"
      SOKETI_DEFAULT_APP_ID: "${PUSHER_APP_ID}"
      SOKETI_DEFAULT_APP_KEY: "${PUSHER_APP_KEY}"
      SOKETI_DEFAULT_APP_SECRET: "${PUSHER_APP_SECRET}"
    networks:
      - coolify
    healthcheck:
      test: ["CMD-SHELL", "wget -qO- http://127.0.0.1:6001/ready && wget -qO- http://127.0.0.1:6002/ready || exit 1"]
      interval: 5s
      retries: 10
      timeout: 2s

