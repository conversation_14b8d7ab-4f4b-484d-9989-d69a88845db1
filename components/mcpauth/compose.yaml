  # Auth management
  mcpauth:
    image: oideibrett/mcpauth:olp
    #container_name: mcpauth
    volumes:
      - ./config/data:/app/data
    environment:
      - PORT=11000
      - CLIENT_ID=${CLIENT_ID}
      - CLIENT_SECRET=${CLIENT_SECRET}
      - OAUTH_DOMAIN=${OAUTH_DOMAIN:-oauth.${DOMAIN}}
      - ALLOWED_SCOPES=openid,email,profile
      - REQUIRED_SCOPES=openid,email
    restart: unless-stopped
    ports:
      - "11000:11000"
